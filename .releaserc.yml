{
  "plugins": [
    "@semantic-release/commit-analyzer",
    "@semantic-release/release-notes-generator",
    "@semantic-release/changelog",
    [
      "@semantic-release/exec",
      {
        "shell": "/bin/bash",
        "verifyReleaseCmd": "mavenSetVersion ${nextRelease.version}",
        "successCmd": "mavenSetVersion $(bumpVersion snapshot 3 ${nextRelease.version})
          && git add pom.xml **/pom.xml
          && git commit -m\"chore(snapshot): bump $(mvn -s /usr/share/maven/ref/settings.xml help:evaluate -Dexpression=project.version -q -DforceStdout) [skip ci]\"
          && git push ${options.repositoryUrl} HEAD:${branch.name}"
      }
    ],
    [
      "@semantic-release/gitlab",
      "gitlabUrl": "https://placide.enedis.fr/",
    ],
    [
      "@semantic-release/git",
      {
        "assets": [ "pom.xml", "CHANGELOG.md", "**/pom.xml" ],
        "message": "chore(release): bump ${nextRelease.version}\n\n${nextRelease.notes}"
      }
    ]
  ],
  "tagFormat": "${version}",
  "branches": [
    {
      "name": "main"
    },
    {
      "name": "/release/+([0-9])?(.{+([0-9]),x})"
    }
  ]
}
