package fr.enedis.i2r.monitoring.infra.watcher;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.time.Duration;
import java.util.List;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import fr.enedis.i2r.monitoring.metrics.MetricsGatherer;
import fr.enedis.i2r.monitoring.metrics.configuration.MetricsConfiguration;
import fr.enedis.i2r.monitoring.metrics.network.SignalStrengthLevel;
import fr.enedis.i2r.monitoring.metrics.ports.MetricsSinkPort;
import fr.enedis.i2r.monitoring.metrics.ports.ModemPort;

class MetricsGathererIntegrationTest {

    private MetricsSinkPort sink;
    private ModemPort modem;
    private MetricsGatherer metricsGatherer;
    private HeartbeatMonitor heartbeatMonitor;

    @BeforeEach
    void setUp() throws Exception {
        sink = mock(MetricsSinkPort.class);
        modem = mock(ModemPort.class);
        when(modem.getSignalLevel()).thenReturn(new SignalStrengthLevel(true, -50));
        
        MetricsConfiguration configuration = new MetricsConfiguration(Duration.ofMillis(100));
        metricsGatherer = new MetricsGatherer(configuration, modem, sink);
        heartbeatMonitor = new HeartbeatMonitor(List.of(metricsGatherer));
    }

    @AfterEach
    void tearDown() {
        if (metricsGatherer != null) {
            metricsGatherer.stop();
        }
    }

    @Test
    void le_HeartbeatMonitor_detecte_que_le_MetricsGatherer_n_est_pas_actif_avant_le_start() {
        assertThat(heartbeatMonitor.areAllTaskAlive()).isFalse();
    }

    @Test
    void le_HeartbeatMonitor_detecte_que_le_MetricsGatherer_est_actif_apres_le_start() {
        metricsGatherer.start();
        assertThat(heartbeatMonitor.areAllTaskAlive()).isTrue();
    }

    @Test
    void le_HeartbeatMonitor_detecte_que_le_MetricsGatherer_n_est_plus_actif_apres_le_stop() {
        metricsGatherer.start();
        assertThat(heartbeatMonitor.areAllTaskAlive()).isTrue();
        
        metricsGatherer.stop();
        assertThat(heartbeatMonitor.areAllTaskAlive()).isFalse();
    }

    @Test
    void le_HeartbeatMonitor_avec_plusieurs_taches_detecte_correctement_l_etat_global() {
        // Créer un deuxième MetricsGatherer
        MetricsGatherer metricsGatherer2 = new MetricsGatherer(
            new MetricsConfiguration(Duration.ofMillis(100)), 
            modem, 
            sink
        );
        
        HeartbeatMonitor monitor = new HeartbeatMonitor(List.of(metricsGatherer, metricsGatherer2));
        
        // Aucune tâche démarrée
        assertThat(monitor.areAllTaskAlive()).isFalse();
        
        // Une seule tâche démarrée
        metricsGatherer.start();
        assertThat(monitor.areAllTaskAlive()).isFalse();
        
        // Toutes les tâches démarrées
        metricsGatherer2.start();
        assertThat(monitor.areAllTaskAlive()).isTrue();
        
        // Une tâche arrêtée
        metricsGatherer.stop();
        assertThat(monitor.areAllTaskAlive()).isFalse();
        
        // Nettoyage
        metricsGatherer2.stop();
    }
}
