package fr.enedis.i2r.monitoring.infra.watcher;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;

import fr.enedis.i2r.monitoring.metrics.ports.Monitored;

class HeartbeatMonitorTest {

    static class StoppedMonitoredTask implements Monitored {
        @Override
        public boolean isRunning() {
            return false;
        }
    }
    static class LiveMonitoredTask implements Monitored {
        @Override
        public boolean isRunning() {
            return true;
        }
    }
    private final List<Monitored> tasks = new ArrayList<>();

    @Test
    void si_une_tache_n_est_pas_active_le_HeartbeatMonitor_areAllTaskAlive_retourne_false() {
        tasks.add(new StoppedMonitoredTask());
        tasks.add(new LiveMonitoredTask());
        var monitor = new HeartbeatMonitor(tasks);
        assertThat(monitor.areAllTaskAlive()).isFalse();
    }

    @Test
    void si_toutes_les_tache_sont_actives_le_HeartbeatMonitor_areAllTaskAlive_retourne_true() {
        tasks.add(new LiveMonitoredTask());
        tasks.add(new LiveMonitoredTask());
        var monitor = new HeartbeatMonitor(tasks);
        assertThat(monitor.areAllTaskAlive()).isTrue();
    }
}
