package fr.enedis.i2r.monitoring.infra.conf;

import java.util.HashMap;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public record Configuration(HashMap<String, String> parameters) {
    private static final Logger logger = LoggerFactory.getLogger(Configuration.class);

    public Optional<String> get(String key) {
        return Optional.ofNullable(parameters.get(key));
    }

    public <T> T parseOrDefault(ParameterWithDefault<T> parameter) {
        try {
            Optional<String> stringValue = get(parameter.primaryKey());
            if (stringValue.isEmpty()) {
                return parameter.defaultValue();
            }

            return parameter.parser().parse(stringValue.get());
        } catch (Exception e) {
            logger.error("Paramètre en base incohérent (%s). %s", parameter.defaultValue().getClass().toString(), e);
            return parameter.defaultValue();
        }
    }
}
