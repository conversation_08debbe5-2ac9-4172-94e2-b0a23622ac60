package fr.enedis.i2r.monitoring.infra.conf;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.HashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.monitoring.infra.conf.providers.MonitoringParameters;
import fr.enedis.i2r.monitoring.metrics.configuration.MonitoringConfiguration;
import fr.enedis.i2r.monitoring.infra.conf.providers.MetricsParameters;
import fr.enedis.i2r.monitoring.metrics.configuration.MetricsConfiguration;

// TODO : Cette classe est un copier coller de celle dans i2R : il faut
// l'intégrer à un dépôt params qui sera récupéré de tous
public class SqliteConfigurationAdapter {
    private static final Logger logger = LoggerFactory.getLogger(SqliteConfigurationAdapter.class);

    private final Connection connection;

    private Configuration configuration;

    public SqliteConfigurationAdapter() throws SQLException {
        this(Constants.DEFAULT_DATABASE_PATH);
    }

    public SqliteConfigurationAdapter(String dbPath) throws SQLException {
        this.connection = DriverManager.getConnection("jdbc:sqlite:" + dbPath);
        ensureTableIsCreated();
        this.configuration = fetchConfiguration();
    }

    private void ensureTableIsCreated() throws SQLException {
        var sql = String.format("""
                CREATE TABLE IF NOT EXISTS %s(
                    %s text not null primary key,
                    %s text not null)""", Constants.PARAMS_TABLE_NAME, Constants.PARAMS_LABEL_COLUMN_NAME,
                Constants.PARAMS_VALUE_COLUMN_NAME);

        try (var pstmt = this.connection.prepareStatement(sql)) {
            pstmt.executeUpdate();
        }
    }

    private Configuration fetchConfiguration() throws SQLException {
        var sql = String.format("SELECT %s, %s FROM %s", Constants.PARAMS_LABEL_COLUMN_NAME,
                Constants.PARAMS_VALUE_COLUMN_NAME, Constants.PARAMS_TABLE_NAME);

        HashMap<String, String> parameters = new HashMap<>();

        try (var stmt = this.connection.createStatement();
             var rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                parameters.put(rs.getString(Constants.PARAMS_LABEL_COLUMN_NAME),
                        rs.getString(Constants.PARAMS_VALUE_COLUMN_NAME));
            }

            return new Configuration(parameters);
        } catch (SQLException e) {
            logger.error("Récupération des paramètres dans la base SQLite", e);
            throw e;
        }
    }

    public MetricsConfiguration fetchMetricsConfiguration() {
        return MetricsParameters.getMetricsConfiguration(configuration);
    }

    public MonitoringConfiguration fetchMonitoringConfiguration() {
        return MonitoringParameters.getMonitoringConfiguration(configuration);
    }
}
