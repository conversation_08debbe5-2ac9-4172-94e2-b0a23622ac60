package fr.enedis.i2r.monitoring.infra.conf.providers;

import java.time.Duration;

import fr.enedis.i2r.monitoring.infra.conf.Configuration;
import fr.enedis.i2r.monitoring.infra.conf.ParameterWithDefault;
import fr.enedis.i2r.monitoring.metrics.configuration.MetricsConfiguration;

public class MetricsParameters {
    public static final ParameterWithDefault<Duration> SIGNAL_CHECK_INTERVAL = new ParameterWithDefault<>(
            "i2r.network.signal-check-interval-secs",
            (valeur) -> {
                long seconds = Long.parseLong(valeur);
                return Duration.ofSeconds(seconds);
            },
            MetricsConfiguration.DEFAULT_SIGNAL_CHECK_INTERVAL);

    public static MetricsConfiguration getMetricsConfiguration(Configuration configuration) {
        return new MetricsConfiguration(
            configuration.parseOrDefault(SIGNAL_CHECK_INTERVAL)
        );

    }
}
 