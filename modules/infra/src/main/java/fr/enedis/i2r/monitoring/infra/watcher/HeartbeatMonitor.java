package fr.enedis.i2r.monitoring.infra.watcher;

import fr.enedis.i2r.monitoring.metrics.ports.HeartbeatMonitorPort;
import fr.enedis.i2r.monitoring.metrics.ports.Monitored;

import java.util.List;

public class HeartbeatMonitor implements HeartbeatMonitorPort {

   private final List<Monitored> tasks;

    public HeartbeatMonitor(List<Monitored> tasks) {
        this.tasks = tasks;
    }

    public boolean areAllTaskAlive() {
        return tasks.stream().filter(Monitored::isRunning).count() == tasks.size();
    }
}
