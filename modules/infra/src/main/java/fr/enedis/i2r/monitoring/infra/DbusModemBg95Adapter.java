package fr.enedis.i2r.monitoring.infra;

import org.freedesktop.dbus.connections.impl.DBusConnectionBuilder;
import org.freedesktop.dbus.exceptions.DBusException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.hal.CurrentSignalTuple;
import fr.enedis.hal.ModemManager1;
import fr.enedis.i2r.monitoring.metrics.network.SignalStrengthLevel;
import fr.enedis.i2r.monitoring.metrics.ports.ModemPort;

public class DbusModemBg95Adapter implements ModemPort {
    private static final Logger logger = LoggerFactory.getLogger(DbusModemBg95Adapter.class);

    private static final String DBUS_MODEM_PATH = "/fr/enedis/HAL/ModemManager1";
    private static final String DBUS_MODEM_NAME = "fr.enedis.HAL.ModemManager1";

    private final ModemManager1 modemManager;

    public DbusModemBg95Adapter(ModemManager1 modemManager) {
        this.modemManager = modemManager;
    }

    public static DbusModemBg95Adapter connect() throws IllegalStateException {
        try {
            var modemManager = DBusConnectionBuilder.forSystemBus().build().getRemoteObject(
                    DBUS_MODEM_NAME,
                    DBUS_MODEM_PATH,
                    ModemManager1.class);

            return new DbusModemBg95Adapter(modemManager);
        } catch (DBusException e) {
            logger.error("Failed to initialize ModemManager instance", e);
            throw new IllegalStateException("Could not connect to DBus Modem Manager", e);
        }
    }

    @Override
    public SignalStrengthLevel getSignalLevel() throws Exception {
        try {
            CurrentSignalTuple response = this.modemManager.CurrentSignal();

            return new SignalStrengthLevel(response.getIsConnected(), response.getSignal().intValue());
        } catch (Exception e) {
            logger.error("échec de l'obtention du signal", e);
            throw e;
        }

    }
}
