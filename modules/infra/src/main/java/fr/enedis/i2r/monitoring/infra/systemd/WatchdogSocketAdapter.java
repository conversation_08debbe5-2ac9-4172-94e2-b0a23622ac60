package fr.enedis.i2r.monitoring.infra.systemd;


import fr.enedis.i2r.monitoring.metrics.ports.WatchdogSocketPort;

public class WatchdogSocketAdapter implements WatchdogSocketPort {

    private final UnixSocket socket;

    public WatchdogSocketAdapter(String socket) throws InvalidSocketTypeException {
        this.socket = new UnixSocket(socket);
    }

    @Override
    public void init() {
        socket.notify("READY=1");
    }

    @Override
    public void heartbeat() {
        socket.notify("WATCHDOG=1");
    }
}
