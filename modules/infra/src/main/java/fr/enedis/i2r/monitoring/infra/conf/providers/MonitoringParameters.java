package fr.enedis.i2r.monitoring.infra.conf.providers;

import fr.enedis.i2r.monitoring.infra.conf.Configuration;
import fr.enedis.i2r.monitoring.infra.conf.ParameterWithDefault;
import fr.enedis.i2r.monitoring.metrics.configuration.MonitoringConfiguration;

import java.time.Duration;

public class MonitoringParameters {
    public static final ParameterWithDefault<Duration> WATCHDOG_INTERVAL_SECS = new ParameterWithDefault<>(
            "i2r.watchdog-interval-secs.i2r-monitoring",
            (valeur) -> {
                long seconds = Long.parseLong(valeur);
                return Duration.ofSeconds(seconds);
            },
            MonitoringConfiguration.DEFAULT_WATCHDOG_INTERVAL_SECS);

    public static MonitoringConfiguration getMonitoringConfiguration(Configuration configuration) {
        return new MonitoringConfiguration(
            configuration.parseOrDefault(WATCHDOG_INTERVAL_SECS)
        );

    }
}
