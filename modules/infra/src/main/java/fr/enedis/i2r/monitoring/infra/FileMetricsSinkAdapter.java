package fr.enedis.i2r.monitoring.infra;

import java.io.File;
import java.io.FileOutputStream;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.monitoring.metrics.MetricType;
import fr.enedis.i2r.monitoring.metrics.ports.MetricsSinkPort;

public class FileMetricsSinkAdapter implements MetricsSinkPort {

    private static final Logger logger = LoggerFactory.getLogger(FileMetricsSinkAdapter.class);
    private Path metricsFolderPath;

    public FileMetricsSinkAdapter(String metricsDirectoryPath) throws Exception {
        File metricsFolder = new File(metricsDirectoryPath);
        if (!metricsFolder.exists()) {
            logger.info("creation du dossier de métriques {}", metricsDirectoryPath);

            if (!metricsFolder.mkdirs()) {
                throw new Exception(
                    String.format("erreur lors de la création du dossier de métriques : %s", metricsDirectoryPath)
                );
            }
        }

        this.metricsFolderPath = Paths.get(metricsDirectoryPath);
    }

    @Override
    public void sendMetric(MetricType type, String content) {
        logger.info("Métrique {} remontée à la valeur {}", type.toString(), content);

        var metricPath = metricsFolderPath.resolve(filenameForMetric(type));

        try (var fileWriter = new FileOutputStream(metricPath.toFile(), false)) {
            fileWriter.write(content.getBytes());
        } catch (Exception e) {
            logger.error("erreur lors de l'écriture de la métrique sur son fichier", e);
        }
    }

    private static String filenameForMetric(MetricType type) {
        switch (type) {
            case SIGNAL_LEVEL:
                return "modem-signal.txt";
            default:
                logger.warn("type de metrique non supportée : {}", type.name());
                return "unsupported.txt";
        }
    }
}
