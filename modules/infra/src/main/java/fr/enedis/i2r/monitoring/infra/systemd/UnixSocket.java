package fr.enedis.i2r.monitoring.infra.systemd;

import java.io.IOException;
import java.nio.ByteBuffer;

import jnr.unixsocket.UnixDatagramChannel;
import jnr.unixsocket.UnixSocketAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Classe utilitaire pour envoyer des messages à une socket Unix domain datagram,
 * typiquement utilisée pour notifier le watchdog de systemd (via la variable d'environnement {@code NOTIFY_SOCKET}).
 * <p>
 * Cette implémentation repose sur la bibliothèque <b>jnr-unixsocket</b> car la JDK standard
 * ne permet pas d'utiliser des sockets Unix domain de type <b>datagram</b> :
 * <ul>
 *   <li>Le support introduit par {@code java.net.UnixDomainSocketAddress} ne fonctionne qu'avec {@code SocketChannel} (SOCK_STREAM).</li>
 *   <li>{@code DatagramChannel} dans la JDK ne supporte pas les adresses de type Unix domain socket, ce qui entraîne une {@code UnsupportedAddressTypeException}.</li>
 * </ul>
 * Pour supporter les sockets de type {@code SOCK_DGRAM} (obligatoires pour le protocole de notification de systemd),
 * on utilise donc {@code UnixDatagramChannel} et {@code UnixSocketAddress} fournis par la bibliothèque jnr-unixsocket.
 * <p>
 * Cette classe gère également les sockets situées dans l'abstract namespace (chemin commençant par {@code @})
 * en préfixant le chemin par {@code \0} comme requis par l'API native.
 */
public class UnixSocket {
    private final Logger logger = LoggerFactory.getLogger(UnixSocket.class);
    private final String socket;

    public UnixSocket(String socket) throws InvalidSocketTypeException {
        String socketPath = socket;

        if (!(socketPath.startsWith("/") || socketPath.startsWith("@"))) {
            logger.error("Unsupported socket type");
            throw new InvalidSocketTypeException();
        }

        if (socketPath.startsWith("@")) {
            socketPath = "\0" + socketPath.substring(1);
        }
        this.socket = socketPath;
    }

    public void notify(String arg) {
        UnixSocketAddress socket = new UnixSocketAddress(this.socket);
        try (UnixDatagramChannel channel = UnixDatagramChannel.open()) {
            channel.send(ByteBuffer.wrap(arg.getBytes()), socket);
            logger.debug("Envoi du message [{}] à la socket [{}]", arg, this.socket);
        } catch (IOException e) {
            logger.error("Erreur de l'envoi du message [{}] à la socket [{}]", arg, this.socket, e);
        }
    }
}
