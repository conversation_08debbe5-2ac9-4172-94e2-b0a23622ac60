package fr.enedis.i2r.monitoring.metrics;

import static fr.enedis.i2r.monitoring.metrics.MetricType.SIGNAL_LEVEL;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.atMost;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.Duration;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import fr.enedis.i2r.monitoring.metrics.configuration.MetricsConfiguration;
import fr.enedis.i2r.monitoring.metrics.network.SignalStrengthLevel;
import fr.enedis.i2r.monitoring.metrics.ports.MetricsSinkPort;
import fr.enedis.i2r.monitoring.metrics.ports.ModemPort;

class MetricsGathererTest {

    private MetricsSinkPort sink;
    private ModemPort modem;
    private MetricsGatherer metricsGatherer;
    private MetricsConfiguration configuration;

    @BeforeEach
    void setUp() throws Exception {
        sink = mock(MetricsSinkPort.class);
        modem = mock(ModemPort.class);
        configuration = new MetricsConfiguration(Duration.ofMillis(100));
        
        when(modem.getSignalLevel()).thenReturn(new SignalStrengthLevel(true, -50));
        
        metricsGatherer = new MetricsGatherer(configuration, modem, sink);
    }

    @AfterEach
    void tearDown() {
        if (metricsGatherer != null) {
            metricsGatherer.stop();
        }
    }

    @Test
    void au_demarrage_le_MetricsGatherer_n_est_pas_en_cours_d_execution() {
        assertThat(metricsGatherer.isRunning()).isFalse();
    }

    @Test
    void apres_start_le_MetricsGatherer_est_en_cours_d_execution() {
        metricsGatherer.start();
        assertThat(metricsGatherer.isRunning()).isTrue();
    }

    @Test
    void apres_stop_le_MetricsGatherer_n_est_plus_en_cours_d_execution() {
        metricsGatherer.start();
        assertThat(metricsGatherer.isRunning()).isTrue();
        
        metricsGatherer.stop();
        assertThat(metricsGatherer.isRunning()).isFalse();
    }

    @Test
    void le_MetricsGatherer_execute_periodiquement_la_tache_de_signal_modem() throws Exception {
        CountDownLatch latch = new CountDownLatch(2); // Attendre au moins 2 exécutions
        
        doAnswer(invocation -> {
            latch.countDown();
            return null;
        }).when(sink).sendMetric(any(), any());

        metricsGatherer.start();
        
        boolean completed = latch.await(1, TimeUnit.SECONDS);
        assertThat(completed).isTrue();
        
        verify(modem, atLeast(2)).getSignalLevel();
        verify(sink, atLeast(2)).sendMetric(SIGNAL_LEVEL, "-50");
    }

    @Test
    void le_MetricsGatherer_peut_etre_redemarrer_apres_un_stop() throws Exception {
        // Premier démarrage
        metricsGatherer.start();
        assertThat(metricsGatherer.isRunning()).isTrue();
        
        // Arrêt
        metricsGatherer.stop();
        assertThat(metricsGatherer.isRunning()).isFalse();
        
        // Redémarrage - doit créer un nouveau MetricsGatherer car le scheduler est fermé
        metricsGatherer = new MetricsGatherer(configuration, modem, sink);
        metricsGatherer.start();
        assertThat(metricsGatherer.isRunning()).isTrue();
    }

    @Test
    void appeler_start_plusieurs_fois_ne_cree_pas_plusieurs_taches() throws Exception {
        CountDownLatch latch = new CountDownLatch(3);
        
        doAnswer(invocation -> {
            latch.countDown();
            return null;
        }).when(sink).sendMetric(any(), any());

        metricsGatherer.start();
        metricsGatherer.start(); // Deuxième appel - ne devrait pas créer une nouvelle tâche
        metricsGatherer.start(); // Troisième appel - ne devrait pas créer une nouvelle tâche
        
        boolean completed = latch.await(500, TimeUnit.MILLISECONDS);
        assertThat(completed).isTrue();
        
        // Vérifier qu'il n'y a qu'une seule tâche qui s'exécute
        Thread.sleep(200);
        verify(modem, atMost(5)).getSignalLevel(); // Pas trop d'appels
    }
}
