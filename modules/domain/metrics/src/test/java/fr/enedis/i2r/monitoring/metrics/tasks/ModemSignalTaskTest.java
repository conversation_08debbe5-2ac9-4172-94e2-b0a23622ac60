package fr.enedis.i2r.monitoring.metrics.tasks;

import fr.enedis.i2r.monitoring.metrics.network.SignalStrengthLevel;
import fr.enedis.i2r.monitoring.metrics.ports.MetricsSinkPort;
import fr.enedis.i2r.monitoring.metrics.ports.ModemPort;
import org.junit.jupiter.api.Test;

import static fr.enedis.i2r.monitoring.metrics.MetricType.SIGNAL_LEVEL;
import static org.mockito.Mockito.*;

class ModemSignalTaskTest {

    MetricsSinkPort sink = mock(MetricsSinkPort.class);
    ModemPort modem = mock(ModemPort.class);
    ModemSignalTask modemSignalTask = new ModemSignalTask(modem, sink);

    @Test
    void le_ModemSignalTask_recupere_le_niveau_de_signal() throws Exception {
        when(modem.getSignalLevel()).thenReturn(new SignalStrengthLevel(true, -50));
        modemSignalTask.run();
        verify(modem).getSignalLevel();
    }

    @Test
    void le_ModemSignalTask_envoit_la_metrique_au_sink() throws Exception {
        when(modem.getSignalLevel()).thenReturn(new SignalStrengthLevel(true, -50));
        modemSignalTask.run();
        verify(sink).sendMetric(SIGNAL_LEVEL,"-50");
    }
}