package fr.enedis.i2r.monitoring.metrics.systemd;

import fr.enedis.i2r.monitoring.metrics.configuration.MonitoringConfiguration;
import fr.enedis.i2r.monitoring.metrics.ports.HeartbeatMonitorPort;
import fr.enedis.i2r.monitoring.metrics.ports.WatchdogSocketPort;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.mockito.Mockito.*;

class WatchdogHeartbeatServiceTest {

    public static final Duration WATCHDOG_PERIOD = Duration.ofSeconds(2);

    private WatchdogSocketPort notifySocket;
    WatchdogHeartbeatService watchdogHeartbeatService;
    HeartbeatMonitorPort heartbeatMonitorPort = mock(HeartbeatMonitorPort.class);

    @BeforeEach
    void setUp() {
        MonitoringConfiguration config = new MonitoringConfiguration(WATCHDOG_PERIOD);
        notifySocket = mock(WatchdogSocketPort.class);
        watchdogHeartbeatService = new WatchdogHeartbeatService(notifySocket, config, heartbeatMonitorPort);
    }

    @Test
    void au_demarrage_du_thread_le_watchdog_est_notifie() throws InterruptedException {
        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(watchdogHeartbeatService);
        Thread.sleep(Duration.ofSeconds(1));
        verify(notifySocket, times(1)).init();

        executor.shutdown();
    }

    @Test
    void si_toutes_les_taches_sont_actives_le_watchdog_est_notifie_periodiquement_en_fonction_de_la_configuration() throws InterruptedException {
        Duration testDuration = Duration.ofSeconds(4);
        CountDownLatch latch = new CountDownLatch((int) (testDuration.toSeconds() / WATCHDOG_PERIOD.toSeconds()));
        doAnswer( invocationOnMock -> {
            latch.countDown();
            return null;
        }).when(notifySocket).heartbeat();
        when(heartbeatMonitorPort.areAllTaskAlive()).thenReturn(true);

        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(watchdogHeartbeatService);

        boolean testCompleted = latch.await(5, TimeUnit.SECONDS);
        executor.shutdown();

        verify(notifySocket, times(2)).heartbeat();
        Assertions.assertThat(testCompleted).isTrue();
    }

    @Test
    void si_des_taches_sont_stopees_le_watchdog_n_est_pas_notifie() throws InterruptedException {
        Duration testDuration = Duration.ofSeconds(4);
        CountDownLatch latch = new CountDownLatch((int) (testDuration.toSeconds() / WATCHDOG_PERIOD.toSeconds()));
        doAnswer( invocationOnMock -> {
            latch.countDown();
            return null;
        }).when(notifySocket).heartbeat();
        when(heartbeatMonitorPort.areAllTaskAlive()).thenReturn(false);

        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(watchdogHeartbeatService);

        latch.await(5, TimeUnit.SECONDS);
        executor.shutdown();

        verify(notifySocket, times(0)).heartbeat();
    }
}
