package fr.enedis.i2r.monitoring.metrics;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import fr.enedis.i2r.monitoring.metrics.configuration.MetricsConfiguration;
import fr.enedis.i2r.monitoring.metrics.ports.MetricsSinkPort;
import fr.enedis.i2r.monitoring.metrics.ports.ModemPort;
import fr.enedis.i2r.monitoring.metrics.ports.Monitored;
import fr.enedis.i2r.monitoring.metrics.tasks.ModemSignalTask;

public class MetricsGatherer implements Monitored {

    private final MetricsConfiguration configuration;
    private final ModemPort modem;
    private final MetricsSinkPort sink;
    private final ScheduledExecutorService scheduler;
    private ScheduledFuture<?> modemSignalTaskFuture;

    public MetricsGatherer(MetricsConfiguration configuration, ModemPort modem, MetricsSinkPort sink) {
        this.configuration = configuration;
        this.modem = modem;
        this.sink = sink;
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "MetricsGatherer-ModemSignal");
            t.setDaemon(true);
            return t;
        });
    }

    public void start() {
        if (modemSignalTaskFuture == null || modemSignalTaskFuture.isCancelled()) {
            modemSignalTaskFuture = scheduler.scheduleAtFixedRate(
                new ModemSignalTask(modem, sink),
                0,
                configuration.signalCheckInterval().toMillis(),
                TimeUnit.MILLISECONDS
            );
        }
    }

    public void stop() {
        if (modemSignalTaskFuture != null) {
            modemSignalTaskFuture.cancel(false);
        }
        scheduler.shutdown();
    }

    @Override
    public boolean isRunning() {
        return modemSignalTaskFuture != null &&
               !modemSignalTaskFuture.isCancelled() &&
               !modemSignalTaskFuture.isDone() &&
               !scheduler.isShutdown();
    }
}
