package fr.enedis.i2r.monitoring.metrics.tasks;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.monitoring.metrics.MetricType;
import fr.enedis.i2r.monitoring.metrics.ports.MetricsSinkPort;
import fr.enedis.i2r.monitoring.metrics.ports.ModemPort;

public class ModemSignalTask implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(ModemSignalTask.class);

    private MetricsSinkPort sink;
    private ModemPort modem;

    public ModemSignalTask(ModemPort modem, MetricsSinkPort sink) {
        this.modem = modem;
        this.sink = sink;
    }

    @Override
    public void run() {
        try {
            var signalLevel = this.modem.getSignalLevel();
            sink.sendMetric(MetricType.SIGNAL_LEVEL, Integer.toString(signalLevel.signalDb()));

            logger.debug("Signal obtenu du modem : {}dB", signalLevel.signalDb());
        } catch (Exception e) {
            logger.error("récolte du signal modem", e);
        }
    } 
}
