package fr.enedis.i2r.monitoring.metrics.systemd;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.monitoring.metrics.configuration.MonitoringConfiguration;
import fr.enedis.i2r.monitoring.metrics.ports.HeartbeatMonitorPort;
import fr.enedis.i2r.monitoring.metrics.ports.WatchdogSocketPort;

public class WatchdogHeartbeatService implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(WatchdogHeartbeatService.class);
    public static final int WITH_NO_DELAY = 0;

    private final WatchdogSocketPort notifySocket;
    private final MonitoringConfiguration configuration;
    private final HeartbeatMonitorPort heartbeatMonitor;
    private final ScheduledExecutorService scheduler;

    public WatchdogHeartbeatService(WatchdogSocketPort notifySocket, MonitoringConfiguration configuration, HeartbeatMonitorPort heartbeatMonitor) {
        this.notifySocket = notifySocket;
        this.configuration = configuration;
        this.heartbeatMonitor = heartbeatMonitor;
        this.scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "WatchdogHeartbeat");
            t.setDaemon(true);
            return t;
        });
    }

    @Override
    public void run() {
        logger.info("Watchdog service démarré");
        notifySocket.init();
        scheduler.scheduleAtFixedRate(
            new Heartbeat(notifySocket, heartbeatMonitor),
            WITH_NO_DELAY,
            configuration.watchdogPeriod().toMillis(),
            TimeUnit.MILLISECONDS
        );
    }

    public void stop() {
        scheduler.shutdown();
    }

    public static class Heartbeat implements Runnable {

        private final WatchdogSocketPort notifySocket;
        private final HeartbeatMonitorPort heartbeatMonitor;

        public Heartbeat(WatchdogSocketPort notifySocket, HeartbeatMonitorPort heartbeatMonitor) {
            this.notifySocket = notifySocket;
            this.heartbeatMonitor = heartbeatMonitor;
        }

        @Override
        public void run() {
            if(heartbeatMonitor.areAllTaskAlive()){
                notifySocket.heartbeat();
            }
        }
    }
}
