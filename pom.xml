<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>fr.enedis.i2r.monitoring</groupId>
    <artifactId>parent</artifactId>
    <version>1.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>21</java.version>
        <sonar.qualitygate.wait>true</sonar.qualitygate.wait>
        <!-- version dépendence interne -->
        <dbus.version>5.1.1-enedis.2</dbus.version>
        <dbus-hal-specs.version>1.2.1</dbus-hal-specs.version>
        <systemd-dbus-api.version>0.1.1-SNAPSHOT</systemd-dbus-api.version>
        <!-- version dépendence technique -->
        <logback.version>1.5.13</logback.version>
        <sqlite-jdbc.version>3.46.1.3</sqlite-jdbc.version>
        <slf4j-api.version>2.0.16</slf4j-api.version>
        <!-- version dépendence de build -->
        <assembly.version>3.7.1</assembly.version>
        <jacoco-maven-plugin.version>0.8.12</jacoco-maven-plugin.version>
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
        <maven-install-plugin.version>3.1.3</maven-install-plugin.version>
        <maven-jar-plugin.version>3.4.2</maven-jar-plugin.version>
        <!-- version dépendence de test -->
        <assertj-core.version>3.26.3</assertj-core.version>
        <cucumber.version>7.20.1</cucumber.version>
        <junit-jupiter.version>5.11.0</junit-jupiter.version>
        <junit-platform.version>1.11.0</junit-platform.version>
        <mockito-core.version>5.14.0</mockito-core.version>
        <awaitility.version>4.3.0</awaitility.version>
    </properties>

    <modules>
        <module>modules</module>
        <module>apps</module>
    </modules>


    <distributionManagement>
        <snapshotRepository>
            <id>delivere-maven-snapshots-deploy</id>
            <name>Deliver-e Snapshots Repo</name>
            <!--suppress UnresolvedMavenProperty -->
            <url>https://${REPOSITOI_ZCA_HOSTNAME}/artifactory/delivere-maven-snapshots</url>
        </snapshotRepository>
        <repository>
            <id>delivere-maven-releases-deploy</id>
            <name>Deliver-e Releases Repo</name>
            <!--suppress UnresolvedMavenProperty -->
            <url>https://${REPOSITOI_ZCA_HOSTNAME}/artifactory/delivere-maven-releases</url>
        </repository>
    </distributionManagement>

    <dependencies>

        <dependency>
            <groupId>com.github.jnr</groupId>
            <artifactId>jnr-unixsocket</artifactId>
            <version>0.38.23</version>
        </dependency>

        <dependency>
            <groupId>fr.enedis.ccma.i2r</groupId>
            <artifactId>systemd-dbus-api</artifactId>
            <version>${systemd-dbus-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.hypfvieh</groupId>
                    <artifactId>dbus-java-transport-jnr-unixsocket</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j-api.version}</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>${logback.version}</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.hypfvieh</groupId>
            <artifactId>dbus-java-core</artifactId>
            <version>${dbus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.hypfvieh</groupId>
            <artifactId>dbus-java-transport-native-unixsocket</artifactId>
            <version>${dbus.version}</version>
        </dependency>
        <dependency>
            <groupId>fr.enedis.ccma.i2r</groupId>
            <artifactId>dbus-hal-specs</artifactId>
            <version>${dbus-hal-specs.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.hypfvieh</groupId>
                    <artifactId>dbus-java-transport-jnr-unixsocket</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
            <version>${sqlite-jdbc.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>${junit-jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit-jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>${mockito-core.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>${assertj-core.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-java</artifactId>
            <version>${cucumber.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-junit-platform-engine</artifactId>
            <version>${cucumber.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-console</artifactId>
            <version>${junit-platform.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-suite</artifactId>
            <version>${junit-platform.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <version>${junit-jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <version>${awaitility.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility-proxy</artifactId>
            <version>3.1.6</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <release>${java.version}</release>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>coverage-initialize</id>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>coverage-report</id>
                            <phase>post-integration-test</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <executions>
                        <execution>
                            <goals>
                                <goal>properties</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin> <!-- pour faire tourner junit (les tests unitaires) -->
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <configuration>
                        <argLine>@{argLine}
                            -javaagent:${settings.localRepository}/org/mockito/mockito-core/${mockito-core.version}/mockito-core-${mockito-core.version}.jar
                        </argLine>
                    </configuration>
                </plugin>
                <plugin> <!-- pour faire tourner cucumber (les tests d'intégration) -->
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <executions>
                        <execution>
                            <goals>
                                <goal>integration-test</goal>
                                <goal>verify</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>${maven-install-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco-maven-plugin.version}</version>
            </plugin>
        </plugins>
    </build>
</project>
