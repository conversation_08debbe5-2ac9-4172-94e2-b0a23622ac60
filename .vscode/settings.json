{
    "editor.formatOnSave": true,
    "java.maven.downloadSources": true,
    "maven.executable.preferMavenWrapper": true,
    "maven.terminal.useJavaHome": true,
    "java.configuration.updateBuildConfiguration": "automatic",
    "java.server.launchMode": "Standard",
    "java.saveActions.organizeImports": true,
    "java.jdt.ls.vmargs": "-Djavax.net.ssl.trustStore=/home/<USER>/.local/jdk/lib/security/cacerts -Djavax.net.ssl.trustStorePassword=changeit",
    "editorconfig.generateAuto": false,
    "java.jdt.ls.lombokSupport.enabled": false,
    "java.jdt.ls.protobufSupport.enabled": false,
    "java.jdt.ls.androidSupport.enabled": "off",
    "java.import.gradle.enabled": false,
    "java.signatureHelp.description.enabled": true,
    "java.symbols.includeSourceMethodDeclarations": true,
    "java.configuration.maven.globalSettings": ".mvn/wrapper/settings.xml",
    "java.configuration.maven.userSettings": ".mvn/wrapper/settings.xml",
    "java.compile.nullAnalysis.mode": "automatic",
    "[java]": {
        // Le formattage Java de VSCode est désastreux. En attendant de trouver une solution, on le désactive
        "editor.formatOnSave": false,
        "editor.defaultFormatter": "redhat.java"
    }
}
