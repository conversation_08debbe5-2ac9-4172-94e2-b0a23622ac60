# I2R-Monitoring

## Contribuer au projet avec les DevContainers

A partir du moment où vous avez WSL et Docker installés et configurés sur votre
machine, vous pouvez contribuer au projet très simplement grâce aux
devcontainers.

Il y a un prérequis cependant : vous devez vous fournir vos identifiants d'accès
à delivere-maven-releases sur l'Artifactory ZCI : `ARTIFACTORY_USER` et
`ARTIFACTORY_TOKEN`.

Une fois ces informations en votre possession :

- Clonez ce projet dans votre WSL
- Dans VSCode, ouvrez le dossier de votre projet
- Configurez l'artifactory
  - Copiez le fichier `.devcontainer/secrets.env.sample` vers le fichier
    `.devcontainer/secrets.env`, en prenant soin de remplir les deux
    informations mentionnées précédemment
- Installez les extensions nécessaires
  - Installez l'extension "Dev Containers"
  - Une fois installée, dans ses paramètres, cochez la case "Execute in WSL"
- En bas à gauche de votre IDE, cliquez sur le petit icône `><`, et faites
  "Reopen in container"

Votre environnement se met en place, patientez, c'est prêt.
