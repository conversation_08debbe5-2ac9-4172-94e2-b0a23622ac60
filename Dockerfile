# Le hostname d'artifactory à utiliser
ARG ARTIFACTORY_URL=artifactory-zci.enedis.fr

# L'URL du registre
ARG REGISTRY_URL=delivere-docker-stages.${ARTIFACTORY_URL}

FROM ${REGISTRY_URL}/embedded-hal:bin AS embedded-hal
FROM ${REGISTRY_URL}/i2r-network:bin AS i2r-network
FROM ${REGISTRY_URL}/i2r-java:base AS dev

ARG ARTIFACTORY_URL
USER wudi

RUN mkdir -p /home/<USER>/.local/bin
ENV PATH=/home/<USER>/.local/bin:$PATH

# ajout de embedded-hal et i2r-network pour plus de praticité
COPY --from=embedded-hal --chown=wudi:wudi /app/embedded-hal /home/<USER>/.local/bin/
COPY --from=i2r-network --chown=wudi:wudi /app/i2r-network /home/<USER>/.local/bin/

# Installation de socat et minicom, pour échanger et simuler des communications série
RUN sudo apt update && sudo apt install -y -q --no-install-recommends \
    socat \
    minicom \
    chrony \
    libjffi-jni \
    && sudo apt clean && sudo rm -rf /var/lib/apt/lists/*

# bashrc de l'utilisateur
COPY .devcontainer/.bashrc /home/<USER>/.bashrc

# installation de chrony, le serveur de temps
COPY --chown=wudi:wudi .devcontainer/chrony/chrony.conf /etc/chrony/chrony.conf

## installation de deno, un runtime sécurisé de Javascript
RUN mkdir -p /home/<USER>/.local/bin
COPY --from=remote-docker-hub.artifactory-zci.enedis.fr/denoland/deno:bin-2.1.10 --chown=wudi:wudi /deno /home/<USER>/.local/bin/deno
ENV DENO_TLS_CA_STORE=system
ENV NPM_CONFIG_REGISTRY=https://artifactory-zci.enedis.fr/artifactory/api/npm/remote-npm-npmjs/
ENV PATH=/home/<USER>/.local/bin:$PATH

## bashrc de l'utilisateur
COPY .devcontainer/.bashrc /root/.bashrc

## installation de chrony, le serveur de temps
COPY --chown=wudi:wudi .devcontainer/chrony/chrony.conf /etc/chrony/chrony.conf

## création du dossier de métriques
RUN mkdir /var/lib/i2r/metrics
