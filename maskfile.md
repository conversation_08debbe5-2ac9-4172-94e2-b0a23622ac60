# Commandes du projet i2R

Ce fichier recense toutes les commandes utiles au développement et au build du
projet i2R. Vous pouvez :

- Copier-coller les commandes depuis ce fichier
- Utiliser l'outil en ligne de commande [mask](https://github.com/jacobdeichert/mask), qui se base sur ce fichier afin d'executer les commandes
  - Par exemple : `mask run boitier-ui`

## clean

> Cette commande permet de nettoyer les artefacts Java de ce dépot.

```sh
./mvnw clean
```

## run

> Concerne le lancement des applications i2R.

### run boitier-ui

> Démarre l'interface du boitier UI

```sh
cd apps/boitier-ui
deno task start
```

### run main

> Démarre l'application i2R

```sh
echo "Compilation du main en cours ..."
mask install
echo "Compilation terminée"
echo "Lancement de i2r-monitoring ..."
./mvnw -f apps/main exec:java
```
### run mainWithSocket

> Démarre l'application i2R avec un socket pour recevoir les notifications du watchdog.

```sh
mkdir -p /tmp/fk_sysstd_w
rm -f /tmp/fk_sysstd_w/noty
socat -u UNIX-RECV:/tmp/fk_sysstd_w/noty STDOUT &
SOCAT_PID=$! 
echo $SOCAT_PID
echo "Compilation du main en cours..."
mask install
echo "Compilation terminée"
echo "Lancement de i2R..."
NOTIFY_SOCKET=/tmp/fk_sysstd_w/noty WATCHDOG_USEC=10000000 ./mvnw -f apps/main exec:java -Djava.library.path=/usr/lib/x86_64-linux-gnu/jni/
kill SOCAT_PID
```

### run embedded-hal

> Démarre le embedded-hal

```sh
echo "Redémarrage du service dbus pour prévenir les erreurs en cas de mauvais arrêt."
sudo service dbus restart
echo "Lancement du embedded-hal ..."
embedded-hal mock 
```

## install

> Raccourci pour un mvn install optimisé, qui ne lance pas les tests

```sh
./mvnw -T 1C install -Dmaven.test.skip=true -DskipTests -q
```

## package

> Raccourci pour un package optimisé

```sh
echo "Packaging des applications ..."
./mvnw clean -q
./mvnw -T 1C package -Dmaven.test.skip=true -DskipTests -q
echo "Terminé."
```

## test

> Lancement de tous les tests de l'application

```sh
mask install
./mvnw test
```

## sql

### sql exec (request)
> Permet de faire une commande SQl

```sh
sqlite3 /var/lib/i2r/i2r-params.db "$request"
```

### sql bipStatus

> Récupère le status du bip en BD

```sh
sqlite3 /var/lib/i2r/i2r-params.db "select * from parameters where param_name = 'i2r.bip.status';"
```
### sql select

> Fait un select sur la table 'parameters' de la BD

```sh
sqlite3 /var/lib/i2r/i2r-params.db "select * from parameters;"
```

## dev

### dev fill-test-data

> Remplit la base de données avec un minimum de données de test

```sh
echo "('v')/ remplissage de la base de données"
sqlite3 /var/lib/i2r/i2r-params.db \
 "CREATE TABLE IF NOT EXISTS parameters(param_name text not null primary key, param_value text not null, updated boolean);" \
 "INSERT INTO parameters(param_name, param_value) VALUES('i2r.si.primary-datacenter', 'pacy') ON CONFLICT(param_name) DO UPDATE SET param_value=excluded.param_value;" \
 "INSERT INTO parameters(param_name, param_value) VALUES('i2r.si.ip.pacy', '127.0.0.1') ON CONFLICT(param_name) DO UPDATE SET param_value=excluded.param_value;" \
 "INSERT INTO parameters(param_name, param_value) VALUES('i2r.si.ip.noe', '127.0.0.1') ON CONFLICT(param_name) DO UPDATE SET param_value=excluded.param_value;" \
 "INSERT INTO parameters(param_name, param_value) VALUES('i2r.bip.status', '1') ON CONFLICT(param_name) DO UPDATE SET param_value=excluded.param_value;"
```
