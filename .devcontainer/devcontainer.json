{"build": {"dockerfile": "../Dockerfile", "context": "..", "target": "dev"}, "runArgs": ["--env-file", "${localWorkspaceFolder}/.devcontainer/secrets.env", "--privileged", "--cap-add=SYS_TIME", "--network=chrony_local_dev_net"], "initializeCommand": {"dockerNetwork": "docker network inspect chrony_local_dev_net || docker network create --subnet=10.5.0.0/16 chrony_local_dev_net"}, "onCreateCommand": {"installDotfiles": "bash .devcontainer/install.sh", "createTestDatabase": "mask dev fill-test-data"}, "postStartCommand": {"startChrony": "sudo service chrony start"}, "postAttachCommand": {"startDbus": "sudo service dbus start"}, "mounts": ["type=bind,source=/home/<USER>/.ssh,target=/home/<USER>/.ssh,readonly"], "forwardPorts": [3000], "remoteUser": "wudi", "containerUser": "wudi", "customizations": {"jetbrains": {"settings": {"com.intellij:app:BuiltInServerOptions.builtInServerPort": 59507, "Docker:app:DockerSettings.dockerComposePath": "docker", "Docker:app:DockerSettings.dockerPath": "docker", "com.intellij:app:HttpConfigurable.use_proxy_pac": true, "com.intellij:app:HttpConfigurable.use_pac_url": true, "com.intellij:app:HttpConfigurable.pac_url": "http://pac-ad-enedis.proxy.edf.fr/"}}, "vscode": {"extensions": ["ms-vsliveshare.vsliveshare", "vscjava.vscode-java-pack", "EditorConfig.EditorConfig", "eamodio.gitlens", "ms-azuretools.vscode-docker", "denoland.vscode-deno", "vscjava.vscode-java-debug@0.58.2024090204", "yzhang.markdown-all-in-one"]}}, "portsAttributes": {"3000": {"label": "Boitier UI"}}}