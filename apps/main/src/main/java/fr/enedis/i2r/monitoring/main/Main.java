package fr.enedis.i2r.monitoring.main;

import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.monitoring.infra.DbusModemBg95Adapter;
import fr.enedis.i2r.monitoring.infra.FileMetricsSinkAdapter;
import fr.enedis.i2r.monitoring.infra.conf.SqliteConfigurationAdapter;
import fr.enedis.i2r.monitoring.infra.systemd.WatchdogSocketAdapter;
import fr.enedis.i2r.monitoring.infra.watcher.HeartbeatMonitor;
import fr.enedis.i2r.monitoring.metrics.MetricsGatherer;
import fr.enedis.i2r.monitoring.metrics.configuration.MetricsConfiguration;
import fr.enedis.i2r.monitoring.metrics.configuration.MonitoringConfiguration;
import fr.enedis.i2r.monitoring.metrics.ports.MetricsSinkPort;
import fr.enedis.i2r.monitoring.metrics.ports.ModemPort;
import fr.enedis.i2r.monitoring.metrics.ports.WatchdogSocketPort;
import fr.enedis.i2r.monitoring.metrics.systemd.WatchdogHeartbeatService;

public class Main {
    private static final Logger logger = LoggerFactory.getLogger(Main.class);

    private static final String METRICS_FOLDER_PATH = "/var/lib/i2r/metrics";
    private static final String DATABASE_FILE_PATH = "/var/lib/i2r/i2r-params.db";


    public static void main(String[] args) {
        try {
            logger.info("Démarrage de i2R-Monitoring..");

            SqliteConfigurationAdapter sqliteConfigurationAdapter = new SqliteConfigurationAdapter(DATABASE_FILE_PATH);
            MetricsSinkPort metricsSinkPort = new FileMetricsSinkAdapter(METRICS_FOLDER_PATH);
            ModemPort modemPort = DbusModemBg95Adapter.connect();

            MonitoringConfiguration monitoringConfiguration = sqliteConfigurationAdapter.fetchMonitoringConfiguration();
            MetricsConfiguration configuration = sqliteConfigurationAdapter.fetchMetricsConfiguration();

            MetricsGatherer metricsGatherer = new MetricsGatherer(configuration, modemPort, metricsSinkPort);
            metricsGatherer.start();

            HeartbeatMonitor monitor = new HeartbeatMonitor(List.of(metricsGatherer));

            Optional<String> watchdogSocketPath = Optional.ofNullable(System.getenv("NOTIFY_SOCKET"));
            if(watchdogSocketPath.isPresent()) {
                WatchdogSocketPort watchdogSocketAdapter = new WatchdogSocketAdapter(watchdogSocketPath.get());
                WatchdogHeartbeatService watchdogHeartbeatService = new WatchdogHeartbeatService(watchdogSocketAdapter, monitoringConfiguration, monitor);
                watchdogHeartbeatService.run();
            }

        } catch (Exception e) {
            logger.error("échec de l'initialisation de i2r-monitoring", e);
        }
    }
}
